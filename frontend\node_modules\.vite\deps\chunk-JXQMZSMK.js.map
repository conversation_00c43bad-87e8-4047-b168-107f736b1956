{"version": 3, "sources": ["../../@mui/material/esm/ImageList/ImageList.js", "../../@mui/material/esm/ImageList/imageListClasses.js"], "sourcesContent": ["'use client';\n\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getImageListUtilityClass } from \"./imageListClasses.js\";\nimport ImageListContext from \"./ImageListContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant]\n  };\n  return composeClasses(slots, getImageListUtilityClass, classes);\n};\nconst ImageListRoot = styled('ul', {\n  name: 'MuiImageList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})({\n  display: 'grid',\n  overflowY: 'auto',\n  listStyle: 'none',\n  padding: 0,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  variants: [{\n    props: {\n      variant: 'masonry'\n    },\n    style: {\n      display: 'block'\n    }\n  }]\n});\nconst ImageList = /*#__PURE__*/React.forwardRef(function ImageList(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageList'\n  });\n  const {\n    children,\n    className,\n    cols = 2,\n    component = 'ul',\n    rowHeight = 'auto',\n    gap = 4,\n    style: styleProp,\n    variant = 'standard',\n    ...other\n  } = props;\n  const contextValue = React.useMemo(() => ({\n    rowHeight,\n    gap,\n    variant\n  }), [rowHeight, gap, variant]);\n  const style = variant === 'masonry' ? {\n    columnCount: cols,\n    columnGap: gap,\n    ...styleProp\n  } : {\n    gridTemplateColumns: `repeat(${cols}, 1fr)`,\n    gap,\n    ...styleProp\n  };\n  const ownerState = {\n    ...props,\n    component,\n    gap,\n    rowHeight,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListRoot, {\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: style,\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(ImageListContext.Provider, {\n      value: contextValue,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `ImageListItem`s.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 2\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The gap between items in px.\n   * @default 4\n   */\n  gap: PropTypes.number,\n  /**\n   * The height of one row in px.\n   * @default 'auto'\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['masonry', 'quilted', 'standard', 'woven']), PropTypes.string])\n} : void 0;\nexport default ImageList;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getImageListUtilityClass(slot) {\n  return generateUtilityClass('MuiImageList', slot);\n}\nconst imageListClasses = generateUtilityClasses('MuiImageList', ['root', 'masonry', 'quilted', 'standard', 'woven']);\nexport default imageListClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,wBAAsB;AACtB,YAAuB;;;ACJhB,SAAS,yBAAyB,MAAM;AAC7C,SAAO,qBAAqB,gBAAgB,IAAI;AAClD;AACA,IAAM,mBAAmB,uBAAuB,gBAAgB,CAAC,QAAQ,WAAW,WAAW,YAAY,OAAO,CAAC;AACnH,IAAO,2BAAQ;;;ADKf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,OAAO;AAAA,EACxB;AACA,SAAO,eAAe,OAAO,0BAA0B,OAAO;AAChE;AACA,IAAM,gBAAgB,eAAO,MAAM;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,CAAC;AAAA,EACjD;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS;AAAA;AAAA,EAET,yBAAyB;AAAA,EACzB,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,YAA+B,iBAAW,SAASA,WAAU,SAAS,KAAK;AAC/E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,eAAqB,cAAQ,OAAO;AAAA,IACxC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;AAC7B,QAAM,QAAQ,YAAY,YAAY;AAAA,IACpC,aAAa;AAAA,IACb,WAAW;AAAA,IACX,GAAG;AAAA,EACL,IAAI;AAAA,IACF,qBAAqB,UAAU,IAAI;AAAA,IACnC;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,eAAe;AAAA,IACtC,IAAI;AAAA,IACJ,WAAW,aAAK,QAAQ,MAAM,QAAQ,OAAO,GAAG,SAAS;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,cAAuB,mBAAAA,KAAK,yBAAiB,UAAU;AAAA,MACrD,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,UAAU,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnF,UAAU,kBAAAC,QAAgD,KAAK;AAAA;AAAA;AAAA;AAAA,EAI/D,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,KAAK,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,WAAW,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAI5E,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,YAAY,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AACrJ,IAAI;AACJ,IAAO,oBAAQ;", "names": ["ImageList", "_jsx", "PropTypes"]}