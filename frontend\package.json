{"name": "my-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fortawesome/fontawesome-free": "^6.7.2", "@heroui/react": "^2.8.0", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.34.1", "aos": "^2.3.4", "axios": "^1.11.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "motion": "^12.23.12", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.6.0", "react-player": "^3.3.2", "react-router-dom": "6.22.0", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "three": "^0.180.0", "vanta": "^0.5.24", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "daisyui": "^5.1.10", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}